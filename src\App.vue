<template>
  <div class="container">
    <div class="map-info">
      <p>当前地图: {{ currentMap.name }}</p>
      <p>当前代码: {{ currentMap.adcode }}</p>
      <p v-if="currentMap.navList.length > 1" class="info-nav">
        <span>层级导航: </span>
        <span v-for="(nav, index) in currentMap.navList" :key="nav.adcode" class="nav-item">
          <span class="item-title" @click="handleLevelChange(nav, index)">{{ nav.name }}</span>
          <span class="item-arrow">-></span>
        </span>
      </p>
      <p>当前飞线: {{ lines.length }}</p>
      <button class="line-btn" @click="handleAddLine(currentMap.name)">随机添加飞线</button>
    </div>
    <div ref="chartRef" class="map-chart"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import "echarts-gl";

import { useECharts } from "./hooks/useEcharts";
import { useMap } from "./hooks/useMap";
import { useLine } from "./hooks/useLine";

const chartRef = ref();
const { setOption, getInstance } = useECharts(chartRef, true);
const { points, lines, handleAddLine, resetLineData } = useLine(setOption);
const { currentMap, registerMap, handleMapClick, handleLevelChange } = useMap(setOption, resetLineData);

// 图表静态配置
const chartOptions: any = {
  backgroundColor: "#000000",
  // 参考https://echarts.apache.org/zh/option.html#geo3D
  geo3D: {
    map: currentMap.name,
    roam: true,
    shading: 'realistic',
    regionHeight: 6,
    boxDepth: 120,
    viewControl: {
      distance: 150,
      alpha: 45,
      beta: 0,
      panSensitivity: 1,
      zoomSensitivity: 1,
      rotateSensitivity: 1,
    },
    label: {
      show: true,
      color: "#6AD4DD",
      fontSize: 16,
      fontFamily: '微软雅黑',
      backgroundColor: "rgba(0,0,0,0)",
    },
    itemStyle: {
      borderColor: "#207fce",
      borderWidth: 0.8,
      areaColor: '#244779',
      opacity: 0.9,
    },
    emphasis: {
      label: {
        color: "#ffffff",
        fontSize: 18,
      },
      itemStyle: {
        areaColor: "#2884db",
        borderWidth: 1,
        borderColor: '#ffffff',
      },
    },
    // 真实感材质相关配置
    realisticMaterial: {
      detailTexture: '#fff',
      textureTiling: 1,
      roughness: 0.1,
      metalness: 0.1,
    },
    // 光照配置
    light: {
      main: {
        intensity: 1.2,
        shadow: true,
        shadowQuality: 'medium',
        alpha: 40,
        beta: 40,
      },
      ambient: {
        intensity: 0.3,
      },
    },
  },
  series: [
    // 参考https://echarts.apache.org/zh/option.html#series-scatter3D
    {
      type: "scatter3D",
      coordinateSystem: "geo3D",
      symbolSize: 12,
      itemStyle: {
        color: "#93EBF8",
        opacity: 0.9,
      },
      emphasis: {
        itemStyle: {
          color: "#ffffff",
        },
      },
      data: points.value,
    },
    // 参考https://echarts.apache.org/zh/option.html#series-lines3D
    {
      type: "lines3D",
      coordinateSystem: "geo3D",
      effect: {
        show: true,
        period: 4,
        trailLength: 0.2,
        trailWidth: 6,
        trailOpacity: 0.8,
        trailColor: "#93EBF8",
      },
      lineStyle: {
        color: "#93EBF8",
        width: 3,
        opacity: 0.7,
      },
      data: lines.value,
    },
    // 添加柱状图3D效果
    {
      type: "bar3D",
      coordinateSystem: "geo3D",
      barSize: 0.6,
      minHeight: 2,
      silent: true,
      itemStyle: {
        color: "rgba(255, 165, 0, 0.8)",
        opacity: 0.6,
      },
      data: [],
    },
  ],
};

// 页面加载完成
onMounted(async () => {
  // 注册地图
  await registerMap();
  // 绑定点击事件
  const instance = getInstance();
  instance?.on("click", handleMapClick);
  setOption(chartOptions);
});
</script>

<style lang="less">
* {
  box-sizing: border-box;
}
html,
body {
  width: 100%;
  height: 100%;
  line-height: 1;
}
#app {
  width: 100%;
  height: 100%;
  min-width: 1280px;
  color: #000000;
  font-size: 12px;
  font-family: "'Pingfang SC', 'SF UI Text', 'Helvetica Neue', 'Consolas'";
}
.container {
  width: 100%;
  height: 100%;
  position: relative;
  .map-info {
    height: 300px;
    color: red;
    font-weight: 600;
    font-size: 18px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9;
    .nav-item {
      .item-title {
        cursor: pointer;
      }
      &:last-child {
        .item-arrow {
          display: none;
        }
      }
    }
    .line-btn {
      outline: none;
      cursor: pointer;
    }
  }
  .map-chart {
    width: 100%;
    height: 100%;
  }
}
</style>
